import { access } from "@/access";
import { genPreviewPath } from "@/utils/gen-preview-path";
import type { CollectionConfig } from "payload";
import {
	MetaDescriptionField,
	MetaImageField,
	MetaTitleField,
	OverviewField,
	PreviewField,
} from "@payloadcms/plugin-seo/fields";
import { slugField } from "@/fields/slug";
import { baseBlocks } from "@/blocks";
import {
	FixedToolbarFeature,
	HeadingFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";

export const Pages: CollectionConfig = {
	slug: "pages",
	labels: {
		singular: "Seite",
		plural: "Seiten",
	},
	access: {
		create: access.authenticated,
		delete: access.authenticated,
		read: access.authenticatedOrPublished,
		update: access.authenticated,
	},
	defaultPopulate: {
		title: true,
		slug: true,
	},
	admin: {
		defaultColumns: ["title", "slug", "updatedAt"],
		livePreview: {
			url: ({ data }) =>
				genPreviewPath({
					slug: typeof data?.slug === "string" ? data.slug : "",
					collection: "pages",
				}),
		},
		preview: (data) =>
			genPreviewPath({
				slug: typeof data?.slug === "string" ? data.slug : "",
				collection: "pages",
			}),
		useAsTitle: "title",
	},
	fields: [
		{
			name: "title",
			label: "Seitentitel",
			type: "text",
			required: true,
		},
		{
			type: "tabs",
			tabs: [
				{
					label: "Hero",
					fields: [
						{
							name: "hero",
							type: "group",
							fields: [
								{
									name: "type",
									type: "select",
									defaultValue: "low-impact",
									label: "Variante",
									options: [
										{ label: "Kein Hero", value: "none" },
										{ label: "großer Eindruck", value: "high-impact" },
										{
											label: "geringer Eindruck",
											value: "low-impact",
										},
										{ label: "Über-uns-Seite Hero", value: "about-hero" },
										{ label: "Konzertseite Hero", value: "concert-hero" },
									],
									required: true,
								},
								{
									name: "title",
									type: "text",
									label: "Titel",
									admin: {
										condition: (_, siblingData) =>
											Boolean(
												[
													"high-impact",
													"low-impact",
													"concert-hero",
													"about-hero",
												].includes(siblingData?.type),
											),
									},
									required: true,
								},
								{
									name: "content",
									type: "text",
									label: "Kurzbeschreibung im Hero",
									admin: {
										condition: (_, siblingData) =>
											Boolean(
												[
													"high-impact",
													"low-impact",
													"concert-hero",
													"about-hero",
												].includes(siblingData?.type),
											),
									},
								},
								{
									name: "richText",
									type: "richText",
									label: "Detaillierter Text im Hero",
									editor: lexicalEditor({
										features: ({ rootFeatures }) => [
											...rootFeatures,
											HeadingFeature({
												enabledHeadingSizes: ["h2", "h3", "h4"],
											}),
											FixedToolbarFeature(),
											InlineToolbarFeature(),
											FontColorFeature(),
										],
									}),
									admin: {
										condition: (_, siblingData) =>
											siblingData?.type === "about-hero",
									},
								},
								{
									type: "row",
									fields: [
										{
											name: "image",
											type: "upload",
											relationTo: "media",
											label: "Hintergrundbild",
											required: true,
											hasMany: false,
											admin: {
												width: "50%",
												condition: (_, siblingData) =>
													["high-impact", "low-impact"].includes(
														siblingData?.type,
													),
											},
										},
										{
											name: "variant",
											type: "select",
											label: "Bildvariante",
											admin: {
												width: "50%",
												condition: (_, siblingData) =>
													siblingData?.type === "low-impact",
											},
											options: [
												{ label: "Standard", value: "standard" },
												{ label: "Abgedunkelt", value: "dimmed" },
												{ label: "Multipliziert", value: "multiplied" },
											],
										},
									],
								},
							],
						},
					],
				},
				{
					label: "Inhalt",
					fields: [
						{
							name: "layout",
							type: "blocks",
							label: "Seiteninhalt",
							blocks: baseBlocks,
							required: true,
						},
					],
				},
				{
					name: "meta",
					label: "SEO",
					fields: [
						OverviewField({
							titlePath: "meta.title",
							descriptionPath: "meta.description",
							imagePath: "meta.image",
						}),
						MetaTitleField({ hasGenerateFn: true }),
						MetaImageField({ relationTo: "media" }),
						MetaDescriptionField({}),
						PreviewField({
							hasGenerateFn: true,
							titlePath: "meta.title",
							descriptionPath: "meta.description",
						}),
					],
				},
			],
		},
		{
			name: "publishedAt",
			type: "date",
			label: "Veröffentlichungsdatum",
			admin: {
				position: "sidebar",
			},
		},
		...slugField(),
	],
	versions: {
		drafts: {
			autosave: {
				interval: 100,
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};

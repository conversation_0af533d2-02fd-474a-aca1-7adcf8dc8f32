import type { CollectionConfig } from "payload";

export const Programs: CollectionConfig = {
	slug: "programs",
	labels: {
		singular: "Programm",
		plural: "Programme",
	},
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "segments", "updatedAt"],
	},
	fields: [
		{
			name: "title",
			label: "Titel",
			type: "text",
		},
		{
			name: "description",
			label: "Beschreibung",
			type: "richText",
		},
		{
			name: "segments",
			label: "Abschnitte",
			type: "array",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
				},
			],
		},
	],
};

import type { CollectionConfig } from "payload";
import { format, parseISO } from "date-fns";
import { de } from "date-fns/locale";

const formatConcertDate = (
	dates: { date: string; startTime: string }[],
): string => {
	if (!dates.length) return "";

	const sorted = dates
		.map((d) => ({ ...d, dateObj: parseISO(d.date) }))
		.sort((a, b) => a.dateObj.getTime() - b.dateObj.getTime());

	const groups: Record<string, typeof sorted> = {};
	for (const d of sorted) {
		const key = format(d.dateObj, "MMMM yyyy", { locale: de });
		if (!groups[key]) groups[key] = [];
		groups[key].push(d);
	}

	const result = Object.entries(groups).map(([monthYear, days]) => {
		const datesFormatted = days
			.map((d) => format(d.dateObj, "EEE, d.", { locale: de }))
			.join(" & ");
		const time = days[0].startTime;
		return `${datesFormatted} ${monthYear} | ${time}`;
	});

	return result.join(", ");
};

export const Concerts: CollectionConfig = {
	slug: "concerts",
	labels: {
		singular: "Konzert",
		plural: "Konzerte",
	},
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "where", "formattedDateString"],
	},
	fields: [
		{
			name: "title",
			label: "Titel",
			type: "text",
			required: true,
		},
		{
			name: "subline",
			label: "Untertitel",
			type: "text",
		},
		{
			name: "image",
			relationTo: "media",
			label: "Bild",
			type: "upload",
			required: true,
		},
		{
			name: "isUpcoming",
			type: "checkbox",
			defaultValue: false,
			admin: { hidden: true },
		},
		{
			name: "dates",
			label: "Daten & Uhrzeiten",
			type: "array",
			required: true,
			fields: [
				{
					name: "date",
					label: "Datum",
					type: "date",
					required: true,
				},
				{
					name: "startTime",
					label: "Uhrzeit",
					type: "text",
					required: true,
				},
			],
		},
		{
			name: "formattedDateString",
			type: "text",
			label: "Wann?",
			admin: {
				hidden: true,
			},
		},
		{
			name: "where",
			label: "Wo?",
			type: "text",
			required: true,
		},
		{
			name: "cancelled",
			label: "Abgesagt?",
			type: "checkbox",
			defaultValue: false,
		},
	],
	hooks: {
		beforeChange: [
			async ({ data }) => {
				if (!data.dates || !Array.isArray(data.dates)) return data;

				const formatted = formatConcertDate(data.dates);
				return {
					...data,
					formattedDateString: formatted,
				};
			},
		],
	},
};

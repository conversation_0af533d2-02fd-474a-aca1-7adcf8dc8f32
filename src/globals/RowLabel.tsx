"use client";

import type { Head<PERSON> } from "@/payload-types";
import { type RowLabelProps, useRowLabel } from "@payloadcms/ui";

export const RowLabel: React.FC<RowLabelProps> = () => {
	console.log("amk");
	const data = useRowLabel<NonNullable<Header["navItems"]>[number]>();

	const label = data?.data?.link?.label
		? `Nav Element ${data.rowNumber !== undefined ? data.rowNumber + 1 : ""}: ${data?.data?.link?.label}`
		: data?.data?.label
			? `Nav Element ${data.rowNumber !== undefined ? data.rowNumber + 1 : ""}: ${data?.data?.label} (${data?.data?.children?.length} Unterseiten)`
			: "";

	return <div>{label}</div>;
};

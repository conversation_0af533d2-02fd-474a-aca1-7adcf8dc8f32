"use client";

import { type FC, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import gsap from "gsap";
import { CMSLink } from "../cms-link";
import type { Header } from "@/payload-types";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useGSAP } from "@gsap/react";

interface MegaNavProps {
	navRef: React.RefObject<HTMLDivElement>;
	item: NonNullable<NonNullable<Header["navItems"]>[number]>;
	children: React.ReactNode;
	onClose: () => void;
}

export const MegaNav: FC<MegaNavProps> = ({ children, navRef, item }) => {
	const [isAnimating, setIsAnimating] = useState(false);
	const pathname = usePathname();

	const menuRef = useRef(null);
	const menuOverlayRef = useRef(null);
	const menuItemsRef = useRef(null);

	const menuBtnRef = useRef(null);
	const closeBtnRef = useRef(null);

	useGSAP(
		() => {
			gsap.set(menuOverlayRef.current, {
				opacity: 0,
				pointerEvents: "none",
			});
			gsap.set(closeBtnRef.current, { y: "100%" });
			gsap.set(".menu-overlay-items a", { y: "100%" });
		},
		{ scope: menuRef },
	);

	const openMenu = () => {
		if (isAnimating) return;
		setIsAnimating(true);

		const tl = gsap.timeline({ onComplete: () => setIsAnimating(false) });
		tl.to(menuBtnRef.current, {
			y: "-100%",
			duration: 0.5,
			ease: "power3.out",
			onComplete: () => {
				navRef.current.style.pointerEvents = "none";
				gsap.set(menuBtnRef.current, { y: "100%" });
			},
		})
			.to(
				menuOverlayRef.current,
				{
					opacity: 1,
					duration: 0.5,
					ease: "power3.out",
					onStart: () => {
						menuOverlayRef.current.style.pointerEvents = "all";
					},
				},
				"-=0.45",
			)
			.to(
				closeBtnRef.current,
				{ y: "0%", duration: 1, ease: "power3.out" },
				"-=0.5",
			)
			.to(
				".menu-overlay-items a",
				{
					y: "0%",
					duration: 1,
					stagger: 0.075,
					ease: "power3.out",
				},
				"<",
			);
	};

	const closeMenu = () => {
		if (isAnimating) return;
		setIsAnimating(true);

		const tl = gsap.timeline({ onComplete: () => setIsAnimating(false) });
		tl.to(closeBtnRef.current, {
			y: "-100%",
			duration: 0.5,
			ease: "power3.out",
		})
			.to(
				".menu-overlay-items a",
				{
					y: "-100%",
					duration: 0.5,
					stagger: 0.05,
					ease: "power3.in",
				},
				"<",
			)
			.to(
				menuOverlayRef.current,
				{
					opacity: 0,
					duration: 0.5,
					ease: "power3.out",
					onComplete: () => {
						menuOverlayRef.current.style.pointerEvents = "none";
						gsap.set(closeBtnRef.current, { y: "100%" });
						gsap.set(".menu-overlay-items .revealer h3", { y: "100%" });
						gsap.set(".menu-footer .revealer p, .menu-footer .revealer h3", {
							y: "100%",
						});
					},
				},
				"+=0.1",
			)
			.to(
				menuBtnRef.current,
				{
					y: "0%",
					duration: 0.5,
					ease: "power3.out",
					onStart: () => {
						navRef.current.style.pointerEvents = "all";
					},
				},
				"-=0.45",
			);
	};

	return (
		<>
			<button
				type="button"
				className="relative w-max [clip-path:polygon(0_0,100%_0,100%_100%,0_100%)]"
				onClick={openMenu}
			>
				<p className="text-sm" ref={menuBtnRef}>
					{children}
				</p>
			</button>
			<div
				ref={menuRef}
				className="fixed top-0 left-0 w-screen h-[100svh] pointer-events-none z-[10000]"
			>
				<div
					ref={menuOverlayRef}
					className="fixed top-0 left-0 w-screen h-[100svh] overflow-hidden bg-primary text-secondary will-change-[opacity] pointer-events-none z-20 opacity-0"
				>
					<div className="fixed top-0 left-0 w-screen p-6 flex gap-6">
						<div className="flex-1 flex justify-end h-max cursor-pointer">
							<button
								type="button"
								className="relative w-max [clip-path:polygon(0_0,100%_0,100%_100%,0_100%)]"
								onClick={closeMenu}
							>
								<p className="text-xs uppercase " ref={closeBtnRef}>
									Close
								</p>
							</button>
						</div>
					</div>

					<div
						ref={menuItemsRef}
						className="menu-overlay-items group w-3/4 px-6 pb-6 pt-2 flex flex-wrap gap-x-4 items-start max-[1000px]:flex-col max-[1000px]:p-6"
					>
						{item.children?.map((item) => {
							const path = `/${
								item.link.type === "reference"
									? typeof item.link.reference?.value === "object"
										? item.link.reference?.value?.slug
										: item.link.url
									: item.link.url
							}`;

							return (
								<div
									key={item.id}
									className={cn(
										"relative w-max [clip-path:polygon(0_0,100%_0,100%_100%,0_100%)] opacity-100 group-hover:opacity-50 hover:opacity-100 transition-opacity duration-300",
										path === pathname && "!opacity-25",
									)}
								>
									<CMSLink
										{...item.link}
										label={null}
										className={cn(
											"inline-block",
											path === pathname && "!cursor-default",
										)}
									>
										<h2>{item.link.label}</h2>
									</CMSLink>
								</div>
							);
						})}
					</div>
				</div>
			</div>
		</>
	);
};

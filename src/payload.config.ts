import { mongooseAdapter } from "@payloadcms/db-mongodb";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import path from "node:path";
import { buildConfig } from "payload";
import { fileURLToPath } from "node:url";
import sharp from "sharp";
import { en } from "@payloadcms/translations/languages/en";
import { de } from "@payloadcms/translations/languages/de";

import * as Collections from "./collections";
import * as Plugins from "./plugins";
import { Header } from "./globals/Header";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default buildConfig({
	admin: {
		user: Collections.Users.slug,
		importMap: {
			baseDir: path.resolve(dirname),
		},
		components: {
			graphics: {
				Icon: "@/components/admin/icon#Icon",
				Logo: "@/components/admin/logo#Logo",
			},
		},
		autoLogin:
			process.env.PAYLOAD_AUTO_LOGIN === "true"
				? {
						email: process.env.PAYLOAD_AUTO_LOGIN_EMAIL || "admin@localhost",
						password: process.env.PAYLOAD_AUTO_LOGIN_PASSWORD || "admin",
						prefillOnly: true,
					}
				: undefined,
		livePreview: {
			collections: ["pages"],
			breakpoints: [
				{
					label: "Mobile",
					name: "mobile",
					width: 375,
					height: 667,
				},
				{
					label: "Tablet",
					name: "tablet",
					width: 768,
					height: 1024,
				},
				{
					label: "Desktop",
					name: "desktop",
					width: 1440,
					height: 900,
				},
			],
		},
	},
	i18n: {
		supportedLanguages: { de, en },
		fallbackLanguage: "de",
	},
	collections: Object.values(Collections),
	globals: [Header],
	editor: lexicalEditor({
		lexical: {
			namespace: "default",
			theme: {
				paragraph: "editor-paragraph",
			},
		},
	}),
	secret: process.env.PAYLOAD_SECRET || "",
	typescript: {
		outputFile: path.resolve(dirname, "payload-types.ts"),
	},
	db: mongooseAdapter({
		url: process.env.DATABASE_URI || "",
	}),
	sharp,
	plugins: Object.values(Plugins),
});

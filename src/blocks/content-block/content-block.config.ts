import type { Block, Field } from "payload";

import {
	FixedToolbarFeature,
	HeadingFeature,
	InlineToolbarFeature,
	lexicalEditor,
	TreeViewFeature,
} from "@payloadcms/richtext-lexical";
import { link } from "@/fields/link";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";

const columnFields: Field[] = [
	{
		name: "richText",
		type: "richText",
		editor: lexicalEditor({
			features: ({ rootFeatures }) => {
				return [
					...rootFeatures,
					HeadingFeature({ enabledHeadingSizes: ["h2", "h3", "h4"] }),
					FixedToolbarFeature(),
					InlineToolbarFeature(),
					FontColorFeature(),
				];
			},
		}),
		label: false,
	},
	{
		name: "width",
		type: "select",
		label: "Breite",
		defaultValue: "8",
		options: [
			{ label: "1 Spalten", value: "1" },
			{ label: "2 Spalten", value: "2" },
			{ label: "3 Spalten", value: "3" },
			{ label: "4 Spalten", value: "4" },
			{ label: "5 Spalten", value: "5" },
			{ label: "6 Spalten", value: "6" },
			{ label: "7 Spalten", value: "7" },
			{ label: "8 Spalten", value: "8" },
			{ label: "9 Spalten", value: "9" },
			{ label: "10 Spalten", value: "10" },
			{ label: "11 Spalten", value: "11" },
			{ label: "12 Spalten", value: "12" },
		],
	},
	{
		name: "enableLink",
		label: "Verlinken",
		type: "checkbox",
	},
	link({
		overrides: {
			admin: {
				condition: (_data, siblingData) => {
					return Boolean(siblingData?.enableLink);
				},
			},
		},
	}),
];

export const ContentBlockConfig: Block = {
	slug: "content",
	imageURL: "/blocks/content-block.png",
	labels: {
		singular: "Inhalt Sektion",
		plural: "Inhalt Sektionen",
	},
	fields: [
		{
			name: "indent",
			type: "select",
			label: "Einzug",
			defaultValue: "oneSixth",
			options: [
				{ label: "Kein Einzug", value: "none" },
				{ label: "2 Spalten", value: "oneSixth" },
				{ label: "4 Spalten", value: "oneThird" },
				{ label: "6 Spalten", value: "half" },
			],
		},
		{
			name: "columns",
			label: "Spalten",
			type: "array",
			admin: {
				initCollapsed: true,
			},
			fields: columnFields,
		},
	],
	interfaceName: "ContentBlock",
};

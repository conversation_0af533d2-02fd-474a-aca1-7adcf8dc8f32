"use client";

import { useRef, type FC } from "react";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as IPunkScrollerBlockProps } from "@/payload-types";

import { gsap } from "gsap";
import { use<PERSON>enis } from "lenis/react";
import { useGSAP } from "@gsap/react";
import { Media } from "@/components/render/render-media";

const SVG: FC<React.ComponentProps<"svg">> = ({ ...props }) => {
	return (
		<svg
			width="467"
			height="640"
			viewBox="0 0 467 640"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<title>punk</title>
			<path
				d="M348.94 419.014C341.285 403.306 342.091 398.473 343.568 394.311C345.045 390.149 350.284 385.584 351.358 382.496C352.433 379.408 352.03 369.071 351.761 357.525C351.492 344.502 350.284 334.835 350.418 325.437C350.552 314.831 350.418 299.392 342.493 280.059C341.956 278.448 340.479 275.36 340.345 271.869C340.345 271.198 340.613 270.258 341.016 269.05L353.373 248.24C354.447 246.495 355.522 244.615 356.731 242.601L467 57.1931L288.366 208.097C288.366 208.097 287.963 208.5 287.695 208.634L283.8 211.991C282.188 212.93 280.442 213.468 278.293 212.93C276.681 212.259 274.532 211.454 272.517 210.648C271.577 210.245 270.906 209.977 269.966 209.574C267.817 208.769 266.205 207.158 265.265 202.324C252.639 128.618 250.49 145.131 222.822 0C222.822 0 191.393 145.668 175.948 207.292C175.41 208.5 174.605 209.574 173.53 209.977C168.023 211.319 162.919 213.065 158.219 215.347C156.607 215.616 154.726 214.944 152.175 212.662C123.701 182.186 53.5902 111.836 51.5755 111.164C49.6951 110.493 81.124 197.894 100.33 250.925C100.599 251.999 100.599 253.073 100.465 253.879C100.33 254.819 99.6589 256.161 98.853 257.369C90.6601 267.304 84.0788 276.568 77.9005 289.457C77.3632 289.859 76.6917 289.994 75.3486 290.396C75.2143 290.396 74.9456 290.396 74.8113 290.531L0 294.693L69.7075 348.798C69.7075 348.798 70.2447 349.604 70.379 350.006C70.782 374.172 72.7966 393.237 72.7966 393.237C76.5574 405.454 87.3023 420.759 82.1984 429.217C77.0946 437.676 49.1579 468.152 46.3374 472.18C43.5168 476.342 59.3655 504.267 68.3644 522.66C77.3632 541.053 111.075 610.195 111.075 610.195L136.729 592.742C136.729 592.742 155.667 608.987 165.606 612.343C176.082 615.968 183.066 616.639 183.066 616.639L196.094 640L205.093 613.283C206.033 596.501 208.854 584.821 211.137 579.45C215.301 569.247 241.626 558.909 251.296 557.567C260.967 556.224 277.756 562.803 285.948 556.493C299.245 546.155 296.425 530.447 298.842 523.331C301.26 516.216 307.707 507.086 305.961 503.596C304.215 500.105 303.006 497.017 303.006 497.017C303.006 497.017 304.886 496.749 307.707 493.392C310.393 490.036 319.795 475.402 325.167 474.73C330.674 474.193 333.898 479.027 341.016 477.953C348.135 477.013 353.776 475.804 357.805 464.258C361.834 452.712 356.865 434.185 349.209 418.477"
				fill="currentColor"
			/>
		</svg>
	);
};

export const PunkScrollerBlock: FC<IPunkScrollerBlockProps> = ({ image }) => {
	const containerRef = useRef<HTMLDivElement | null>(null);
	const trailRef = useRef<HTMLDivElement | null>(null);
	const maskRef = useRef<HTMLDivElement | null>(null);

	useGSAP(
		() => {
			if (!maskRef.current) return;

			const isSmallViewport = window.matchMedia("(max-width: 768px)").matches;

			maskRef.current.style.setProperty(
				"mask-size",
				isSmallViewport ? "349px 480px" : "467px 640px",
			);
			maskRef.current.style.setProperty(
				"-webkit-mask-size",
				isSmallViewport ? "349px 480px" : "467px 640px",
			);

			if (isSmallViewport) return;

			const width = isSmallViewport
				? window.innerWidth * 6
				: window.innerWidth * 3;

			const val = `${width}px ${width}px`;

			gsap.to(maskRef.current, {
				maskSize: val,
				WebkitMaskSize: val,
				ease: "none",
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top top",
					end: "bottom bottom",
					scrub: true,
					invalidateOnRefresh: true,
				},
			});
		},
		{ scope: containerRef },
	);

	useLenis(({ velocity }) => {
		if (!trailRef.current) return;

		const isSmallViewport = window.matchMedia("(max-width: 768px)").matches;

		const trailItems = gsap.utils.toArray(
			trailRef.current?.children,
		) as SVGElement[];

		for (const [i, item] of trailItems.entries()) {
			const index = i + 1;

			gsap.to(item, {
				y: velocity * (isSmallViewport ? 1 : 2) * index,
				ease: "none",
				duration: 0.1,
			});
		}
	});

	return (
		<section ref={containerRef} className="relative h-auto md:h-[250svh]">
			<div className="sticky top-0 h-[800px] md:h-screen">
				<div
					className="absolute inset-0 flex items-center justify-center"
					ref={trailRef}
				>
					<div className="z-[4] absolute">
						<SVG className="text-black max-sm:w-[349px] max-sm:h-[480px]" />
					</div>
					<div className="z-[3] absolute">
						<SVG className="text-[#95C11F] max-sm:w-[349px] max-sm:h-[480px]" />
					</div>
					<div className="z-[2] absolute">
						<SVG className="text-black max-sm:w-[349px] max-sm:h-[480px]" />
					</div>
					<div className="z-[1] absolute">
						<SVG className="text-[#a4a4a4] max-sm:w-[349px] max-sm:h-[480px]" />
					</div>
				</div>
				<div
					className="h-full overflow-hidden [mask-repeat:no-repeat] [mask-position:center_center] relative z-[4]"
					style={{
						maskImage: "url(/punk_filled.svg)",
						WebkitMaskImage: "url(/punk_filled.svg)",
						maskSize: "349px 480px",
						WebkitMaskSize: "349px 480px",
					}}
					ref={maskRef}
				>
					<Media
						resource={image}
						className="h-full w-full"
						imgClassName="h-full w-full object-cover"
					/>
				</div>
			</div>
		</section>
	);
};
